/* Modern Light Theme for Classroom */
:root {
    --primary-color: #4285F4;
    --primary-hover: #3367D6;
    --secondary-color: #5f6368;
    --background-color: #F5F5F5;
    --sidebar-bg: #ffffff;
    --sidebar-active-bg: #E8F0FE;
    --sidebar-active-color: #4285F4;
    --header-bg: #ffffff;
    --card-bg: #ffffff;
    --text-color: #202124;
    --text-secondary: #5f6368;
    --border-color: #DADCE0;
    --success-color: #0F9D58;
    --warning-color: #F4B400;
    --danger-color: #DB4437;
    --info-color: #4285F4;
    --light-hover: #F1F3F4;
    --card-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
    --button-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
}

body {
    font-family: 'Google Sans', 'Roboto', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* Header */
.navbar {
    background-color: var(--header-bg);
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
    padding: 0.5rem 1.5rem;
    height: 64px;
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.navbar-brand {
    display: flex;
    align-items: center;
    color: var(--text-color);
    font-weight: 500;
    font-size: 1.25rem;
    padding: 0;
    margin-right: 2rem;
}

.navbar-brand i {
    color: var(--primary-color);
    margin-right: 12px;
    font-size: 24px;
}

.navbar-light .navbar-brand {
    color: var(--text-color);
}

.navbar-light .navbar-nav .nav-link {
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: color 0.2s ease;
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:focus {
    color: var(--text-color);
}

.dropdown-menu {
    background-color: var(--card-bg);
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    padding: 8px 0;
    margin-top: 8px;
}

.dropdown-item {
    color: var(--text-color);
    padding: 8px 16px;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.dropdown-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    color: var(--text-secondary);
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: var(--light-hover);
    color: var(--text-color);
}

.dropdown-divider {
    border-top: 1px solid var(--border-color);
    margin: 4px 0;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 64px;
    bottom: 0;
    left: 0;
    width: 256px;
    background-color: var(--sidebar-bg);
    z-index: 100;
    overflow-y: auto;
    border-right: 1px solid var(--border-color);
    padding-top: 16px;
}

.sidebar .nav-link {
    color: var(--text-color);
    padding: 12px 24px;
    font-weight: 500;
    display: flex;
    align-items: center;
    border-radius: 0 24px 24px 0;
    margin: 4px 0;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.sidebar .nav-link i {
    margin-right: 16px;
    width: 24px;
    text-align: center;
    font-size: 18px;
    color: var(--text-secondary);
}

.sidebar .nav-link:hover {
    background-color: var(--light-hover);
    color: var(--text-color);
    text-decoration: none;
}

.sidebar .nav-link.active {
    background-color: var(--sidebar-active-bg);
    color: var(--sidebar-active-color);
    font-weight: 500;
}

.sidebar .nav-link.active i {
    color: var(--sidebar-active-color);
}

/* Main content */
.main-content {
    margin-left: 256px;
    padding: 24px;
    padding-top: 88px; /* Add padding to account for fixed navbar */
}

/* Fix for container padding inside main content */
.main-content .container-fluid {
    padding-top: 1.5rem !important;
}

/* Page header */
.page-header {
    margin-bottom: 24px;
}

.page-header h1 {
    font-size: 24px;
    font-weight: 500;
    margin: 0;
    color: var(--text-color);
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: none;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    margin-bottom: 24px;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.15), 0 8px 12px 4px rgba(60, 64, 67, 0.1);
}

.card-header {
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 16px 20px;
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h5 {
    margin: 0;
    font-weight: 500;
    font-size: 16px;
}

.card-body {
    padding: 20px;
}

.card-footer {
    background-color: var(--card-bg);
    border-top: 1px solid var(--border-color);
    padding: 12px 20px;
    display: flex;
    align-items: center;
}

/* Buttons */
.btn {
    font-weight: 500;
    border-radius: 4px;
    padding: 8px 16px;
    transition: all 0.2s ease;
    box-shadow: var(--button-shadow);
    text-transform: none;
    letter-spacing: 0.25px;
}

.btn:focus, .btn:active {
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
}

.btn-sm {
    padding: 4px 12px;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #0b8043;
    border-color: #0b8043;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c53929;
    border-color: #c53929;
}

.btn-secondary {
    background-color: #f1f3f4;
    border-color: #f1f3f4;
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #e8eaed;
    border-color: #e8eaed;
    color: var(--text-color);
}

/* Forms */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 10px 12px;
    height: auto;
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
}

.form-group label {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
    font-size: 14px;
}

.custom-control-label {
    font-weight: normal;
    color: var(--text-color);
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.custom-checkbox .custom-control-label::before {
    border-radius: 2px;
}

.form-text {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Tables */
.table {
    color: var(--text-color);
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    border-bottom: 1px solid var(--border-color);
    font-weight: 500;
    color: var(--text-secondary);
    background-color: #f8f9fa;
    padding: 12px 16px;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    border-top: 1px solid var(--border-color);
    padding: 16px;
    vertical-align: middle;
    font-size: 14px;
}

.table-hover tbody tr:hover {
    background-color: var(--light-hover);
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

/* Status badges */
.badge {
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 16px;
    font-size: 12px;
    letter-spacing: 0.25px;
}

.badge-success {
    background-color: var(--success-color);
    color: white;
}

.badge-danger {
    background-color: var(--danger-color);
    color: white;
}

.badge-warning {
    background-color: var(--warning-color);
    color: #212529;
}

.badge-info {
    background-color: var(--info-color);
    color: white;
}

.badge-secondary {
    background-color: #e8eaed;
    color: var(--text-color);
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 64px 0;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    margin: 24px 0;
}

.empty-state-icon {
    font-size: 72px;
    color: var(--text-secondary);
    margin-bottom: 24px;
    opacity: 0.7;
}

.empty-state-text {
    color: var(--text-secondary);
    margin-bottom: 24px;
    font-size: 16px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state h3 {
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--text-color);
}

/* User avatar */
.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 16px;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
}

/* Chart tabs */
.chart-tabs .btn {
    min-width: 100px;
    transition: all 0.2s ease;
}

.chart-tabs .btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Course cards */
.course-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
}

.course-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    overflow: hidden;
}

.course-card:hover {
    transform: translateY(-4px);
}

.course-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background-color: var(--primary-color);
}

.course-card-header {
    padding: 24px 20px 16px;
    border-bottom: 1px solid var(--border-color);
}

.course-card-title {
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 4px;
    color: var(--text-color);
}

.course-card-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
}

.course-card-body {
    padding: 16px 20px;
    flex-grow: 1;
}

.course-card-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

/* Dashboard styles */
.dashboard-card {
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.bg-primary-light {
    background-color: rgba(66, 133, 244, 0.1);
}

.bg-success-light {
    background-color: rgba(15, 157, 88, 0.1);
}

.bg-warning-light {
    background-color: rgba(244, 180, 0, 0.1);
}

.bg-danger-light {
    background-color: rgba(219, 68, 55, 0.1);
}

.bg-info-light {
    background-color: rgba(66, 133, 244, 0.1);
}

.progress {
    background-color: #f1f3f4;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    border-radius: 10px;
}

/* Print styles */
@media print {
    .navbar, .sidebar, .footer, button, .btn, .dropdown-toggle {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .dashboard-section {
        page-break-after: always;
    }

    .card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    body {
        background-color: white !important;
    }

    .col-md-3 {
        width: 25% !important;
        float: left !important;
    }

    .col-md-4 {
        width: 33.33% !important;
        float: left !important;
    }

    .col-md-6 {
        width: 50% !important;
        float: left !important;
    }

    .col-md-8 {
        width: 66.66% !important;
        float: left !important;
    }

    .row::after {
        content: "";
        display: table;
        clear: both;
    }
}

/* Utilities */
.text-muted {
    color: var(--text-secondary) !important;
}

.border-top {
    border-top: 1px solid var(--border-color) !important;
}

.border-bottom {
    border-bottom: 1px solid var(--border-color) !important;
}

/* Course view styles */
.course-header {
    background-color: var(--primary-color);
    color: white;
    padding: 32px 24px;
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
}

.course-title {
    font-size: 28px;
    font-weight: 500;
    margin: 0 0 8px;
}

.course-description {
    margin: 0;
    opacity: 0.9;
    max-width: 800px;
}

.course-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 24px;
}

.course-tab {
    padding: 16px 24px;
    color: var(--text-color);
    font-weight: 500;
    position: relative;
    transition: color 0.2s ease;
}

.course-tab:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.course-tab.active {
    color: var(--primary-color);
}

.course-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background-color: var(--primary-color);
}

/* Stream items */
.stream-item {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    margin-bottom: 24px;
    overflow: hidden;
}

.stream-item-header {
    padding: 16px 20px;
    display: flex;
    align-items: flex-start;
    border-bottom: 1px solid var(--border-color);
}

.stream-item-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--light-hover);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--primary-color);
    font-size: 18px;
}

.stream-item-title {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 4px;
    color: var(--text-color);
}

.stream-item-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
}

.stream-item-body {
    padding: 16px 20px;
}

.stream-item-footer {
    padding: 12px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

/* Modal styles */
.modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 16px 20px;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 16px 20px;
}

.modal-title {
    font-weight: 500;
    color: var(--text-color);
}

.close {
    color: var(--text-secondary);
    opacity: 0.7;
}

.close:hover {
    color: var(--text-color);
    opacity: 1;
}

/* List group */
.list-group-item {
    border-color: var(--border-color);
    padding: 16px 20px;
}

.list-group-item-action:hover {
    background-color: var(--light-hover);
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        width: 100%;
        position: relative;
        top: 0;
        height: auto;
        padding-top: 0;
        margin-top: 64px; /* Account for navbar height */
    }

    .main-content {
        margin-left: 0;
        padding-top: 88px; /* Ensure content is below navbar on mobile */
    }

    /* Fix navbar on mobile */
    .navbar {
        z-index: 1050;
    }

    .course-grid {
        grid-template-columns: 1fr;
    }

    .course-tabs {
        flex-wrap: wrap;
    }

    .course-tab {
        padding: 12px 16px;
    }
}
